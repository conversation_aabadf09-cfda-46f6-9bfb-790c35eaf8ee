<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam">
      <template #tableHeader="{}">
        <el-button type="primary" @click="openWpMilestoneModal('进度填报')">{{ $t("进度填报") }}</el-button>
        <el-button type="primary" @click="openChangeModal('变更任务')">{{ $t("变更任务") }}</el-button>
      </template>
    </ProTable>
    <WpMilestoneModal ref="WpMilestoneModalRef" />
    <ChangeTaskModal ref="ChangeTaskModalRef" />
  </div>
</template>

<script setup lang="tsx" name="quality-target-qt-milestone">
import { editQtMilestone, getQtMilestoneDetail, listToBeSubmitted } from "@/api/modules/quality-target/qt_milestone";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import WpMilestoneModal from "./components/WpMilestoneModal.vue";
import { ElMessage } from "element-plus";
import { useDict } from "@/hooks/useDict";
import { QtMilestone } from "@/typings/quality-target/qt_milestone";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { ref, reactive, watch, onMounted, getCurrentInstance } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { ReportResultList, Frequency, ReportStatusList } from "@/enums/statusQualityTarget";
import { useRoute } from "vue-router";
import LinkModal from "../../components/LinkModal.vue";
import { getQualityTargetList, qtTargetList } from "@/utils/qtTargetService";
import ChangeTaskModal from "./components/ChangeTaskModal.vue";
import { createQtChange, editQtChange } from "@/api/modules/quality-target/qt_change";

const route = useRoute();

// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const WpMilestoneModalRef = ref<InstanceType<typeof WpMilestoneModal> | null>(null);
const ChangeTaskModalRef = ref<InstanceType<typeof ChangeTaskModal>>();
let queryParams = reactive<QtMilestone.IQueryParams>({} as QtMilestone.IQueryParams);

const initParam = reactive({});
const { wp_improve_type } = useDict("wp_improve_type");

// 表格配置项
const columns = reactive<ColumnProps<QtMilestone.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "year",
    label: "年度",
    width: 80,
    search: {
      el: "date-picker",
      props: { type: "year", valueFormat: "YYYY" },
      order: 1
    }
  },
  {
    prop: "targetNo",
    label: "目标编号",
    width: 180,
    render({ row }) {
      return <LinkModal name={row.targetNo} row={row} onOpenDialog={openPreview} />;
    }
  },
  {
    prop: "targetName",
    label: "质量目标",
    width: 180,
    search: { el: "select", order: 2 },
    enum: qtTargetList,
    fieldNames: { label: "targetName", value: "targetName" }
  },

  {
    prop: "reportStatus",
    label: "状态",
    width: 100,
    tag: true,
    search: { el: "select", order: 3 },
    enum: ReportStatusList,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "dept", label: "分部", width: 120 },
  { prop: "responsibleStaffName", label: "责任人", width: 120 },
  {
    prop: "target",
    label: "目标值",
    width: 100,
    render: ({ row }) => {
      if (row.isPercent === "是") {
        return `${row.target}%`;
      }
      return row.target;
    }
  },
  {
    prop: "reportFreq",
    label: "汇报频率",
    width: 110,
    search: { el: "select", order: 6 },
    enum: Frequency,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "milestone", label: "里程碑", width: 110 },
  {
    prop: "reportScore",
    label: "汇报数据",
    width: 120,
    render: ({ row }) => {
      if (row.isPercent === "1") {
        return `${row.reportScore}%`;
      }
      return row.reportScore;
    }
  },
  {
    prop: "reportResult",
    label: "结果",
    width: 120,
    tag: true,
    search: { el: "select", order: 4 },
    enum: ReportResultList,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "improveLink",
    label: "改善链接",
    width: 120,
    render({ row }) {
      if (row.improveLink && row.improveType) {
        let linkUrl = "";

        if (row.improveType === "CAR") {
          const carNoUrl = import.meta.env.VITE_CAR_NO_URL;
          linkUrl = `${carNoUrl}/problem?mode=view&no=${row.improveLink}`;
        } else if (row.improveType === "8D") {
          const eightDUrl = import.meta.env.VITE_8D_NO_URL;
          linkUrl = `${eightDUrl}/#/report/detail?reportNo=${row.improveLink}`;
        }

        if (linkUrl) {
          return h(
            "a",
            {
              href: linkUrl,
              target: "_blank",
              rel: "noopener noreferrer",
              style: "color: #409eff; text-decoration: underline;"
            },
            row.improveLink
          );
        }
      }
      return row.improveLink || "";
    }
  },
  { prop: "startDate", label: "开始日期", width: 180 },
  { prop: "endDate", label: "结束日期", width: 180 }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;
  queryParams = reactive(condition);
  return listToBeSubmitted({
    condition,
    pageNum,
    pageSize
  });
};

const openPreview = (row: Partial<QtMilestone.Item> = {}) => {
  openWpMilestoneModal("填报", row);
};

const openChangeModal = (title: string) => {
  check();
  const form = { ...currentRow.value };
  if (![`待填报`].includes(form.reportStatus)) {
    return ElMessage.error(t("只有待填报状态才允许变更"));
  }
  const params = {
    title: title,
    isView: title === "查看",
    form: form,
    formChange: {},
    api: title === "变更任务" ? createQtChange : title === "编辑" ? editQtChange : undefined,
    getTableList: proTable.value?.getTableList
  };

  ChangeTaskModalRef.value?.acceptParams(params);
};

const openWpMilestoneModal = (title: string, row: Partial<QtMilestone.Item> = {}) => {
  if (isEmptyObj(row)) {
    check();
  }
  const form = !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    wp_improve_type: wp_improve_type.value,
    form,
    api: editQtMilestone,
    getTableList: proTable.value?.getTableList,
    clearSelection: proTable.value?.clearSelection
  };
  WpMilestoneModalRef.value?.acceptParams(params);
};

const instance = getCurrentInstance();
const componentName = instance?.type.name;

const getDetail = async () => {
  if (route.name !== componentName) return;
  const id = Number(route?.query?.id) || 0;
  if (!isEmpty(id)) {
    try {
      const { success, data } = await getQtMilestoneDetail(id);
      if (!success) {
        ElMessage.error(t("填报记录不存在"));
        return;
      }
      openWpMilestoneModal(t("填报"), data);
    } catch (err) {}
  } else {
    WpMilestoneModalRef.value?.setVisible(false);
  }
};
watch(() => route.query?.id, getDetail, { deep: true, immediate: true });
onMounted(() => {
  getQualityTargetList();
});
</script>
